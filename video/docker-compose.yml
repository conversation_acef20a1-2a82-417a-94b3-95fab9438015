version: '3.8'

services:
  # Redis服务
  redis:
    image: redis:7-alpine
    container_name: short_drama_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: short_drama_backend
    ports:
      - "8000:8000"
    volumes:
      - ./storage:/app/storage
      - ./backend:/app
    environment:
      - DEBUG=true
      - DATABASE_URL=sqlite:///./storage/app.db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - redis
    restart: unless-stopped
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Celery Worker服务
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: short_drama_celery
    volumes:
      - ./storage:/app/storage
      - ./backend:/app
    environment:
      - DEBUG=true
      - DATABASE_URL=sqlite:///./storage/app.db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - redis
      - backend
    restart: unless-stopped
    command: celery -A app.tasks.celery worker --loglevel=info

  # Celery Beat服务(定时任务)
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: short_drama_celery_beat
    volumes:
      - ./storage:/app/storage
      - ./backend:/app
    environment:
      - DEBUG=true
      - DATABASE_URL=sqlite:///./storage/app.db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - redis
      - backend
    restart: unless-stopped
    command: celery -A app.tasks.celery beat --loglevel=info

  # 前端服务(开发环境)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: short_drama_frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
    depends_on:
      - backend
    restart: unless-stopped

  # Nginx服务(生产环境)
  nginx:
    image: nginx:alpine
    container_name: short_drama_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
      - ./frontend/dist:/usr/share/nginx/html
      - ./storage:/usr/share/nginx/html/storage
    depends_on:
      - backend
    restart: unless-stopped
    profiles:
      - production

volumes:
  redis_data:

networks:
  default:
    name: short_drama_network
