# OmniParse - 基于MinerU的PDF解析API

## 项目简介
OmniParse是一个基于MinerU和FastAPI构建的高性能PDF解析工具，支持:
- 文档布局分析
- 文本识别(OCR)
- 表格提取
- 公式识别
- 图片内容分析

## 主要功能模块
1. **PDF解析核心** - 基于magic_pdf的文档分析能力
2. **GPU加速** - 利用NVIDIA GPU加速模型推理
3. **REST API** - 提供标准化的HTTP接口
4. **模型管理** - 自动下载和管理所需AI模型

## 构建方式

```
docker build -t mineru-api .
```

或者使用代理：

```
docker build --build-arg http_proxy=http://127.0.0.1:7890 --build-arg https_proxy=http://127.0.0.1:7890 -t mineru-api .
```

## 启动命令

```
docker run --rm -it --gpus=all -v ./paddleocr:/root/.paddleocr -p 8000:8000 mineru-api
```

初次调用 API 时会自动下载 paddleocr 的模型（约数十 MB），其余模型已包含在镜像中。

## API文档

访问Swagger UI:
```
http://localhost:8000/docs
http://127.0.0.1:8000/docs
```

## 配置文件说明

- `magic-pdf.json`: 主配置文件
- `.env`: 环境变量配置
- `requirements.txt`: Python依赖

## 目录结构

```
├── app.py            # FastAPI主应用
├── parse_pdf.py      # PDF解析核心
├── detect_aigc.py    # AI生成内容检测
├── local_infer_ref/  # 本地推理参考
└── docker.sh         # Docker辅助脚本
```